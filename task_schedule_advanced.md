# OCCT Python 学习库 - 详细任务安排表 (进阶计划)

本文档为 OCCT Python 学习库的进阶任务分解，承接《入门计划》，旨在引导开发者深入探索OCCT的高级功能、底层原理及应用框架，最终达到专家级水平。

---

## 第五阶段：高级曲面与特征建模

**目标**: 超越基本实体建模，掌握自由形态曲面（Free-form Surfaces）的创建、高级特征的构建以及对现有几何的修复与处理能力。

### 任务 5.1: `GeomAPI` - 创建高级曲线与曲面

- **目标**: 学习通过点集、约束等方式创建复杂的B样条曲线和曲面。
- **核心类**:
  - `GeomAPI_PointsToBSpline`: 通过一系列点创建B样条曲线。
  - `GeomAPI_PointsToBSplineSurface`: 通过一个点阵创建B样条曲面。
  - `GeomAPI_Interpolate`: 创建经过所有给定点的插值样条曲线。
- **示例焦点**: 读取一个包含空间点坐标的文本文件，然后使用 `GeomAPI_PointsToBSplineSurface` 生成一个拟合这些点的光滑曲面。

### 任务 5.2: `ShapeFix` - 几何修复

- **目标**: 学习处理从外部导入的有瑕疵的模型，修复常见的几何与拓扑错误。
- **核心类**:
  - `ShapeFix_Shape`: 对一个形状进行全面的修复，如修复面、闭合线框等。
  - `ShapeFix_Wire`: 专门用于修复线框（Wire）的工具，如处理边之间的缝隙。
  - `ShapeFix_Solid`: 修复实体，例如通过缝合面来创建实体。
- **示例焦点**: 故意创建一个有微小缝隙的壳（Shell），然后使用 `ShapeFix_Shape` 或 `ShapeFix_Solid` 将其自动修复为一个有效的、闭合的实体。

### 阶段汇总示例 (任务 5.3)

- **目标**: 综合运用高级曲面和修复技术，创建一个有机的、复杂的模型。
- **示例焦点 (`examples/phase_5_advanced_surfaces.py`)**: 
  1. 创建几条复杂的空间曲线（B样条）。
  2. 使用这些曲线作为边界或龙骨，通过放样或蒙皮（Lofting/Skinning）等方式构建一个复杂的曲面（如船体或飞机机翼）。
  3. 使用 `ShapeFix` 确保生成的几何体是水密的（watertight）。
  4. 对模型进行抽壳，并添加一些圆角，最终进行可视化。

---

## 第六阶段：OCAF - OCCT应用框架

**目标**: 学习OCCT最强大的部分——OCAF（OpenCASCADE Application Framework）。它是一个文档/属性模型，用于构建功能完整的、支持参数化、特征历史、撤销/重做（Undo/Redo）和数据持久化的CAD应用程序。

### 任务 6.1: `TDocStd` & `TDataStd` - 文档与标准属性

- **目标**: 理解OCAF的文档结构，学习如何创建文档、标签（Label）以及附加标准属性（如整数、实数、字符串）。
- **核心类**:
  - `TDocStd_Document`: OCAF应用程序的文档对象。
  - `TDF_Label`: 文档树中的节点，用于挂载属性。
  - `TDataStd_Name`, `TDataStd_Integer`, `TDataStd_Real`: 标准的数据属性类型。
- **示例焦点**: 创建一个OCAF文档，在根标签下创建两个子标签，分别命名为“Parameters”和“Shapes”。在“Parameters”下存储一个名为“Length”的 `TDataStd_Real` 属性。

### 任务 6.2: `TNaming` & `TFunction` - 参数化建模

- **目标**: 学习OCAF的核心：如何将形状与属性关联，以及如何通过函数定义它们之间的依赖关系，实现参数化驱动的模型更新。
- **核心类**:
  - `TNaming_NamedShape`: 将一个 `TopoDS_Shape` 存储在文档标签中的标准属性。
  - `TFunction_Function`: 定义一个“函数”，当其引用的输入属性变化时，该函数会自动重新执行以更新输出属性。
- **示例焦点**: 承接上一个任务，创建一个 `TFunction_Function`。该函数引用“Length”属性作为输入，其执行逻辑是创建一个长度为“Length”值的长方体，并将结果存放在“Shapes”标签下的一个 `TNaming_NamedShape` 属性中。演示修改“Length”值后，形状会自动更新。

### 阶段汇总示例 (任务 6.3)

- **目标**: 构建一个微型的参数化CAD应用原型。
- **示例焦点 (`examples/phase_6_parametric_model.py`)**: 
  1. 创建一个OCAF文档。
  2. 在文档中定义多个参数属性（如 `width`, `height`, `depth`, `hole_radius`）。
  3. 创建一个函数，该函数依赖这些参数，执行“创建一个带中心孔的盒子”的逻辑，并将最终形状存入文档。
  4. 提供一个简单的命令行或GUI界面，允许用户修改参数值。
  5. 每次修改后，重新执行函数并更新AIS视图中的模型，从而展示参数化的效果。

---

## 第七阶段：自定义可视化与交互

**目标**: 超越 `SimpleGui`，学习更底层、更灵活的可视化与交互控制，将OCCT视图嵌入到自己的应用程序中，并实现自定义的交互逻辑。

### 任务 7.1: `SelectMgr` - 精细选择控制

- **目标**: 学习如何管理和响应精细的交互选择，如选择物体的面、边或顶点。
- **核心类**:
  - `SelectMgr_Selection`: 代表一个选择事件，可以从中获取被选中的对象和其子形状。
  - `AIS_InteractiveContext`: 学习其更高级的用法，如激活/停用选择模式（面、边、顶点）。
- **示例焦点**: 创建一个程序，允许用户在视图中只选择“面”。当用户点击一个面时，该面高亮，并在控制台打印出该面的类型或哈希码。
