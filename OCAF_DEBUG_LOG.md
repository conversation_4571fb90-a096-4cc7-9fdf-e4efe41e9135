# OCAF 开发调试日志 (OCAF Development Debug Log)

**日期**: 2025-08-17

## 1. 问题概述

在执行《进阶计划》第六阶段“OCAF - OCCT应用框架”时，在任务6.1和6.2中，连续遇到了与 `pythonocc-core` API 使用相关的、无法解决的 `AttributeError` 和 `TypeError`。尽管尝试了多种基于通用API设计模式的解决方案，但均以失败告终。

本文档旨在记录最终受阻的状态，以便后续进行针对性的研究或获取外部帮助。

---

## 2. 受阻任务：任务 6.2 - 参数化建模

- **目标**: 创建一个由 `TDataStd_Real` 属性驱动的参数化盒子模型。
- **最终状态**: 失败。

### 2.1. 最终尝试的代码

```python
# -*- coding: utf-8 -*-

from OCC.Core.TDocStd import TDocStd_Document
from OCC.Core.TDataStd import TDataStd_Real
from OCC.Core.TNaming import TNaming_Builder, TNaming_NamedShape
from OCC.Core.TFunction import TFunction_Driver, TFunction_Function, TFunction_Logbook
from OCC.Core.TDF import TDF_Label, TDF_LabelSequence
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox
from OCC.Core.BRepGProp import brepgprop
from OCC.Core.GProp import GProp_GProps

class BoxDriver(TFunction_Driver):
    # ... (内部实现省略) ...

def run_parametric_example():
    # ... (参数设置部分省略) ...
    doc.CommitCommand()

    # Set up the Function
    doc.NewCommand()
    function_label = doc.Main().FindChild(2, True)
    function_attr = TFunction_Function.Set(function_label)
    result_label = doc.Main().FindChild(3, True)

    # Link the function with its driver and arguments
    logbook = TFunction_Logbook.Get() # <--- FAILING LINE
    logbook.SetFunction(function_label)
    # ... (后续代码省略) ...

if __name__ == "__main__":
    run_parametric_example()
```

### 2.2. 最终错误信息

```
Traceback (most recent call last):
  File "D:\code\pyocc\src\Core\OCAF\example_2_parametric_modeling.py", line 159, in <module>
    run_parametric_example()
  File "D:\code\pyocc\src\Core\OCAF\example_2_parametric_modeling.py", line 111, in run_parametric_example
    logbook = TFunction_Logbook.Get() # Correct way to get the logbook
AttributeError: type object 'TFunction_Logbook' has no attribute 'Get'. Did you mean: 'Set'?
```

### 2.3. 已尝试但失败的解决方案总结

在任务6.1和6.2中，为了解决属性的“设置-获取”和“函数调用”问题，我尝试了以下所有模式，但均告失败：

1.  **对于属性获取**: 
    - `label.FindAttribute(ID, out_attr)`: 无法正确修改传入的 `out_attr` 对象。
    - `TDataStd_Real.Find(label)`: `TDataStd_Real` 类没有 `Find` 方法。

2.  **对于函数日志簿获取**:
    - `TFunction_Driver.GetLogbook()`: `TFunction_Driver` 类没有 `GetLogbook` 静态方法。
    - `TFunction_Logbook.Get()`: `TFunction_Logbook` 类没有 `Get` 静态方法。

---

## 3. 结论

我目前对于 `pythonocc-core` 中OCAF部分的API封装模式的理解存在根本性的、无法自我纠正的偏差。继续尝试只会重复失败。

根据指示，我将停止在OCAF任务上的自主开发，直到获得进一步的指导或问题解决方案。
