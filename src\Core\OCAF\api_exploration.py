# -*- coding: utf-8 -*-

"""
API Exploration Script for OCAF TFunction classes
This script explores the available methods and attributes of TFunction classes
to understand the correct API usage patterns.
"""

from OCC.Core.TFunction import TFunction_Driver, TFunction_Function, TFunction_Logbook
from OCC.Core.TDocStd import TDocStd_Document
from OCC.Core.TDataStd import TDataStd_Real
from OCC.Core.TDF import TDF_Label

def explore_tfunction_api():
    """Explore the TFunction API to understand correct usage patterns."""
    
    print("=== TFunction API Exploration ===\n")
    
    # 1. Explore TFunction_Logbook
    print("1. TFunction_Logbook methods:")
    logbook_methods = [method for method in dir(TFunction_Logbook) if not method.startswith('_')]
    for method in sorted(logbook_methods):
        print(f"   - {method}")
    print()
    
    # 2. Explore TFunction_Driver
    print("2. TFunction_Driver methods:")
    driver_methods = [method for method in dir(TFunction_Driver) if not method.startswith('_')]
    for method in sorted(driver_methods):
        print(f"   - {method}")
    print()
    
    # 3. Explore TFunction_Function
    print("3. TFunction_Function methods:")
    function_methods = [method for method in dir(TFunction_Function) if not method.startswith('_')]
    for method in sorted(function_methods):
        print(f"   - {method}")
    print()
    
    # 4. Try to create instances and see what works
    print("4. Testing instance creation:")
    
    try:
        # Try creating a logbook instance
        logbook = TFunction_Logbook()
        print("   ✓ TFunction_Logbook() constructor works")
    except Exception as e:
        print(f"   ✗ TFunction_Logbook() constructor failed: {e}")
    
    try:
        # Try creating a driver instance
        driver = TFunction_Driver()
        print("   ✓ TFunction_Driver() constructor works")
    except Exception as e:
        print(f"   ✗ TFunction_Driver() constructor failed: {e}")
    
    # 5. Test with a document context
    print("\n5. Testing with document context:")
    
    try:
        doc = TDocStd_Document("test-doc")
        function_label = doc.Main().FindChild(1, True)
        
        # Try TFunction_Function.Set
        function_attr = TFunction_Function.Set(function_label)
        print("   ✓ TFunction_Function.Set() works")
        
        # Try to get the function
        function_attr_retrieved = TFunction_Function()
        if function_label.FindAttribute(TFunction_Function.GetID(), function_attr_retrieved):
            print("   ✓ TFunction_Function retrieval works")
        else:
            print("   ✗ TFunction_Function retrieval failed")
            
    except Exception as e:
        print(f"   ✗ Document context test failed: {e}")

if __name__ == "__main__":
    explore_tfunction_api()
