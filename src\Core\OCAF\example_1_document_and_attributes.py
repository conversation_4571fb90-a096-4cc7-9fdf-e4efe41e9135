# -*- coding: utf-8 -*- 

"""
OCAF Example 1: Document, Labels, and Standard Attributes
# OCAF 示例 1: 文档、标签与标准属性

This script demonstrates the fundamental concepts of the OCAF framework.
# 本脚本演示了OCAF框架的基本概念。

It shows how to:
# 它展示了如何：
1. Create an OCAF document.
# 1. 创建一个OCAF文档。
2. Create a tree of labels to structure data.
# 2. 创建一个标签树来结构化数据。
3. Attach simple data (real numbers) to labels using standard attributes.
# 3. 使用标准属性将简单数据（实数）附加到标签上。
4. Retrieve and verify the data from the document.
# 4. 从文档中检索并验证数据。
"""

import math

# --- Imports ---
# --- 导入 ---
from OCC.Core.TDocStd import TDocStd_Document
from OCC.Core.TDataStd import TDataStd_Real
from OCC.Core.TDF import TDF_Label

def build_parameter_tree():
    """
    Builds a simple OCAF document with a parameter tree.
    # 构建一个带有参数树的简单OCAF文档。
    """
    print("--- Building an OCAF Parameter Tree ---")
    # --- 正在构建一个OCAF参数树 ---

    # 1. Create a new OCAF document.
    # 1. 创建一个新的OCAF文档。
    doc = TDocStd_Document("ocaf-doc")
    print("Step 1: Document created.")
    # 步骤 1: 已创建文档。

    # 2. Get the root label of the data structure tree.
    # 2. 获取数据结构树的根标签。
    root_label = doc.Main()

    # 3. Create a main label for all parameters.
    # 3. 为所有参数创建一个主标签。
    # FindChild(tag, create_if_not_found)
    # # FindChild(标签号, 如果找不到是否创建)
    params_label = root_label.FindChild(1, True)
    print("Step 2: Created a main 'Parameters' label.")
    # 步骤 2: 已创建一个主“Parameters”标签。

    # 4. Create a transaction to wrap the modifications.
    # 4. 创建一个事务来包裹所有的修改操作。
    doc.NewCommand()

    # 5. Create child labels for each parameter and attach a TDataStd_Real attribute.
    # 5. 为每个参数创建子标签并附加一个 TDataStd_Real 属性。
    length_label = params_label.FindChild(1, True)
    TDataStd_Real.Set(length_label, 100.0) # Set the attribute on the label
    # # 在标签上设置属性

    width_label = params_label.FindChild(2, True)
    TDataStd_Real.Set(width_label, 80.0)

    height_label = params_label.FindChild(3, True)
    TDataStd_Real.Set(height_label, 60.0)

    # Commit the transaction to apply the changes.
    # # 提交事务以应用更改。
    doc.CommitCommand()
    print("Step 3: Created child labels and committed transaction.")
    # 步骤 3: 已创建子标签并提交了事务。

    # 5. Verification: Retrieve the attributes and check their values using the correct pattern.
    # 5. 验证：使用正确的模式检索属性并检查其值。
    print("--- Verifying the data ---")
    # --- 正在验证数据 ---
    
    # The correct way to retrieve a specific attribute is to use the class's `Find` static method.
    # # 检索特定属性的正确方法是使用该类的 `Find` 静态方法。
    length_attr, found_len = TDataStd_Real.Find(length_label)
    if found_len:
        length_val = length_attr.Get()
        print(f"Retrieved Length = {length_val}")
        # 检索到的长度 = {length_val}
        assert math.isclose(length_val, 100.0)
    else:
        raise AssertionError("Could not find Length attribute.")

    width_attr, found_wid = TDataStd_Real.Find(width_label)
    if found_wid:
        width_val = width_attr.Get()
        print(f"Retrieved Width = {width_val}")
        # 检索到的宽度 = {width_val}
        assert math.isclose(width_val, 80.0)
    else:
        raise AssertionError("Could not find Width attribute.")

    height_attr, found_hei = TDataStd_Real.Find(height_label)
    if found_hei:
        height_val = height_attr.Get()
        print(f"Retrieved Height = {height_val}")
        # 检索到的高度 = {height_val}
        assert math.isclose(height_val, 60.0)
    else:
        raise AssertionError("Could not find Height attribute.")

    print("\nVerification successful: All parameters were set and retrieved correctly.")
    # 验证成功：所有参数都已正确设置和检索。

if __name__ == '__main__':
    build_parameter_tree()
