# -*- coding: utf-8 -*-

"""
OCAF Example 2: Parametric Modeling with Functions
# OCAF 示例 2: 使用函数进行参数化建模

This script demonstrates the core of OCAF: creating a parametric model where
geometry is driven by data attributes.
# 本脚本演示了OCAF的核心：创建一个由数据属性驱动几何的参数化模型。

It shows how to:
# 它展示了如何：
1. Create a custom Function Driver by inheriting from `TFunction_Driver`.
# 1. 通过继承 `TFunction_Driver` 来创建一个自定义的函数驱动器。
2. Link this driver to a `TFunction_Function` in the document tree.
# 2. 将此驱动器链接到文档树中的一个 `TFunction_Function`。
3. Set dependencies between parameter labels (inputs) and a result label (output).
# 3. 在参数标签（输入）和结果标签（输出）之间设置依赖关系。
4. Execute the function to generate a shape, then modify a parameter and re-execute.
# 4. 执行函数生成一个形状，然后修改一个参数并重新执行。
"""

# --- Imports ---
# --- 导入 ---
from OCC.Core.TDocStd import TDocStd_Document
from OCC.Core.TDataStd import TDataStd_Real
from OCC.Core.TNaming import TNaming_Builder, TNaming_NamedShape
from OCC.Core.TFunction import TFunction_Driver, TFunction_Function, TFunction_Logbook
from OCC.Core.TDF import TDF_Label, TDF_LabelSequence
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox
from OCC.Core.BRepGProp import brepgprop
from OCC.Core.GProp import GProp_GProps

# 1. Define the custom Function Driver
# 1. 定义自定义的函数驱动器
class BoxDriver(TFunction_Driver):
    def __init__(self):
        super(BoxDriver, self).__init__()

    def Execute(self, theFunctionLabel: TDF_Label):
        # This is the core logic of our parametric function.
        # # 这是我们参数化函数的核心逻辑。
        
        # a. Get the input arguments (the labels for length, width, height)
        # a. 获取输入参数（长度、宽度、高度的标签）
        log = self.GetLogbook()
        input_args = log.GetArguments(TFunction_Driver.INPUT_ARGUMENTS)
        length_label = input_args.Value(1)
        width_label = input_args.Value(2)
        height_label = input_args.Value(3)

        # b. Read the real values from the input labels
        # b. 从输入标签中读取实数值
        length_attr, found_len = TDataStd_Real.Find(length_label)
        width_attr, found_wid = TDataStd_Real.Find(width_label)
        height_attr, found_hei = TDataStd_Real.Find(height_label)
        if not (found_len and found_wid and found_hei):
            # This check is important for robustness
            # # 这个检查对于稳健性很重要
            return 1 # Error code
        
        L = length_attr.Get()
        W = width_attr.Get()
        H = height_attr.Get()

        # c. Create the box with these dimensions
        # c. 用这些尺寸创建盒子
        box = BRepPrimAPI_MakeBox(L, W, H).Shape()

        # d. Store the resulting shape in the result label
        # d. 将生成的形状存储在结果标签中
        results = log.GetArguments(TFunction_Driver.RESULTS)
        result_label = results.Value(1)
        builder = TNaming_Builder(result_label)
        builder.Generated(box)

        print(f"  - BoxDriver executed: Created a box of size ({L}, {W}, {H})")
        #   - BoxDriver 已执行：创建了一个尺寸为 (...) 的盒子
        return 0 # Success code

def run_parametric_example():
    print("--- Parametric Modeling Example ---")
    # --- 参数化建模示例 ---

    # 2. Set up the document and parameter labels (similar to example 1)
    # 2. 设置文档和参数标签（与示例1类似）
    doc = TDocStd_Document("ocaf-doc")
    doc.NewCommand() # Start transaction
    # # 开始事务
    params_label = doc.Main().FindChild(1, True)
    length_label = params_label.FindChild(1, True)
    TDataStd_Real.Set(length_label, 100.0)
    width_label = params_label.FindChild(2, True)
    TDataStd_Real.Set(width_label, 80.0)
    height_label = params_label.FindChild(3, True)
    TDataStd_Real.Set(height_label, 60.0)
    doc.CommitCommand() # Commit transaction
    # # 提交事务
    print("Step 1: Set up parameter labels (L=100, W=80, H=60).")
    # 步骤 1: 已设置参数标签 (L=100, W=80, H=60)。

    # 3. Set up the Function
    # 3. 设置函数
    doc.NewCommand()
    function_label = doc.Main().FindChild(2, True)
    function_attr = TFunction_Function.Set(function_label)
    result_label = doc.Main().FindChild(3, True)

    # 4. Link the function with its driver and arguments
    # 4. 将函数与其驱动器和参数链接起来
    logbook = TFunction_Logbook.Get() # Correct way to get the logbook
    # # 获取日志簿的正确方式
    logbook.SetFunction(function_label)
    logbook.AddArgument(TFunction_Driver.INPUT_ARGUMENTS, length_label)
    logbook.AddArgument(TFunction_Driver.INPUT_ARGUMENTS, width_label)
    logbook.AddArgument(TFunction_Driver.INPUT_ARGUMENTS, height_label)
    logbook.AddArgument(TFunction_Driver.RESULTS, result_label)
    driver = BoxDriver()
    logbook.AddDriver(driver)
    doc.CommitCommand()
    print("Step 2: Set up the function, driver, and dependencies.")
    # 步骤 2: 已设置函数、驱动器和依赖关系。

    # 5. Execute the function for the first time
    # 5. 首次执行函数
    print("\n--- First execution ---")
    # --- 首次执行 ---
    assert driver.Execute(function_label) == 0, "First execution failed"

    # 6. Modify a parameter and re-execute
    # 6. 修改一个参数并重新执行
    print("\n--- Modifying a parameter and re-executing ---")
    # --- 正在修改参数并重新执行 ---
    doc.NewCommand()
    TDataStd_Real.Set(length_label, 150.0) # Change length from 100 to 150
    # # 将长度从 100 更改为 150
    doc.CommitCommand()
    print("Parameter 'Length' changed to 150.0.")
    # 参数“长度”已更改为 150.0。
    assert driver.Execute(function_label) == 0, "Second execution failed"

    # 7. Verification
    # 7. 验证
    print("\n--- Verifying the final shape ---")
    # --- 正在验证最终的形状 ---
    result_shape_attr, found_shape = TNaming_NamedShape.Find(result_label)
    assert found_shape, "Could not find the result shape attribute."
    final_shape = result_shape_attr.Get()
    
    props = GProp_GProps()
    brepgprop.VolumeProperties(final_shape, props)
    volume = props.Mass()
    expected_volume = 150.0 * 80.0 * 60.0
    assert abs(volume - expected_volume) < 1e-3, f"Verification failed: Volume was {volume}, expected {expected_volume}"
    print(f"Verification successful: Final volume is {volume:.2f} as expected.")
    # 验证成功：最终体积为 ... 符合预期。

if __name__ == "__main__":
    run_parametric_example()